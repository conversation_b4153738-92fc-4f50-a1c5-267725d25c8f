import React, { useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import Svg, { Circle, Path, G } from "react-native-svg";
import { Poppins_500Medium, useFonts } from "@expo-google-fonts/poppins";

const { width, height } = Dimensions.get("window");

const OrderSuccessScreen = ({ navigation, onTrackOrder, onBackToHome }) => {
  useEffect(() => {
    // Auto navigate to order status after 8 seconds
    const timer = setTimeout(() => {
      if (onTrackOrder) {
        onTrackOrder();
      }
    }, 8000);

    return () => clearTimeout(timer);
  }, [onTrackOrder]);

  const handleBackToHome = () => {
    if (onBackToHome) {
      onBackToHome();
    } else if (navigation) {
      navigation.navigate("TabNavigator", { screen: "Discover" });
    }
  };

  const handleTrackOrder = () => {
    if (onTrackOrder) {
      onTrackOrder();
    } else if (navigation) {
      navigation.navigate("NewOrderStatus");
    }
  };

  const renderDecorationElements = () => {
    return (
      <View style={styles.decorationContainer}>
        {/* Top decorative elements */}
        <View style={[styles.decorativeElement, { top: 80, left: 50 }]}>
          <Svg width="40" height="20" viewBox="0 0 40 20">
            <Path
              d="M5 10 Q20 5 35 10"
              stroke="#E0E0E0"
              strokeWidth="2"
              fill="none"
            />
            <Circle cx="5" cy="10" r="3" fill="#E0E0E0" />
          </Svg>
        </View>

        <View style={[styles.decorativeElement, { top: 60, right: 80 }]}>
          <Svg width="30" height="30" viewBox="0 0 30 30">
            <Path
              d="M5 15 Q15 5 25 15"
              stroke="#E0E0E0"
              strokeWidth="2"
              fill="none"
            />
            <Circle cx="25" cy="15" r="3" fill="#E0E0E0" />
          </Svg>
        </View>

        <View style={[styles.decorativeElement, { top: 100, right: 40 }]}>
          <View style={[styles.smallCircle, { backgroundColor: "#FF6B6B" }]} />
        </View>

        {/* Left side decorative elements */}
        <View style={[styles.decorativeElement, { top: 180, left: 30 }]}>
          <View style={[styles.smallRect, { backgroundColor: "#E0E0E0" }]} />
        </View>

        <View style={[styles.decorativeElement, { top: 220, left: 60 }]}>
          <View
            style={[
              styles.smallRect,
              { backgroundColor: "#E0E0E0", transform: [{ rotate: "45deg" }] },
            ]}
          />
        </View>

        {/* Right side decorative elements */}
        <View style={[styles.decorativeElement, { top: 180, right: 60 }]}>
          <View style={[styles.smallRect, { backgroundColor: "#E0E0E0" }]} />
        </View>

        <View style={[styles.decorativeElement, { top: 220, right: 30 }]}>
          <View
            style={[
              styles.smallRect,
              { backgroundColor: "#E0E0E0", transform: [{ rotate: "45deg" }] },
            ]}
          />
        </View>

        {/* Bottom decorative elements */}
        <View style={[styles.decorativeElement, { bottom: 300, left: 40 }]}>
          <Svg width="50" height="25" viewBox="0 0 50 25">
            <Path
              d="M5 12 Q25 7 45 12"
              stroke="#E0E0E0"
              strokeWidth="2"
              fill="none"
            />
            <Circle cx="5" cy="12" r="3" fill="#E0E0E0" />
          </Svg>
        </View>

        <View style={[styles.decorativeElement, { bottom: 280, left: 80 }]}>
          <View style={[styles.smallCircle, { backgroundColor: "#4285F4" }]} />
        </View>

        <View style={[styles.decorativeElement, { bottom: 320, right: 50 }]}>
          <Svg width="40" height="20" viewBox="0 0 40 20">
            <Path
              d="M5 10 Q20 15 35 10"
              stroke="#E0E0E0"
              strokeWidth="2"
              fill="none"
            />
            <Circle cx="35" cy="10" r="3" fill="#E0E0E0" />
          </Svg>
        </View>

        <View style={[styles.decorativeElement, { bottom: 250, right: 80 }]}>
          <View style={[styles.smallCircle, { backgroundColor: "#FF6B6B" }]} />
        </View>

        <View style={[styles.decorativeElement, { bottom: 280, right: 120 }]}>
          <View style={[styles.smallCircle, { backgroundColor: "#4285F4" }]} />
        </View>

        <View style={[styles.decorativeElement, { bottom: 350, left: 120 }]}>
          <View style={[styles.smallCircle, { backgroundColor: "#E0E0E0" }]} />
        </View>
      </View>
    );
  };

  useFonts({
    Poppins_500Medium,
  });

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {/* Back button */}
      <TouchableOpacity style={styles.backButton} onPress={handleBackToHome}>
        <Ionicons name="chevron-back" size={24} color="#000" />
      </TouchableOpacity>

      {/* Decorative elements */}
      {renderDecorationElements()}

      {/* Main success icon */}
      <View style={styles.successIconContainer}>
        <View style={styles.successCircle}>
          <Ionicons name="checkmark" size={60} color="#5F22D9" />
        </View>
      </View>

      {/* Success message */}
      <View style={styles.messageContainer}>
        <Text style={styles.title}>
          Congrats! Your Order has{"\n"}been placed
        </Text>
        <Text style={styles.subtitle}>
          Your Plenti Surprise pack is on{"\n"}it's way to being processed
        </Text>
      </View>

      {/* Action buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.trackButton} onPress={handleTrackOrder}>
          <Ionicons
            name="bag-outline"
            size={20}
            color="#FFFFFF"
            style={styles.buttonIcon}
          />
          <Text style={styles.trackButtonText}>Track Order Status</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.homeButton} onPress={handleBackToHome}>
          <Ionicons
            name="arrow-back"
            size={16}
            color="#666"
            style={styles.homeButtonIcon}
          />
          <Text style={styles.homeButtonText}>Back to home</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 20,
  },
  backButton: {
    position: "absolute",
    top: 10,
    left: 20,
    zIndex: 10,
    padding: 8,
  },
  decorationContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  decorativeElement: {
    position: "absolute",
  },
  smallCircle: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  smallRect: {
    width: 12,
    height: 4,
    borderRadius: 2,
  },
  successIconContainer: {
    alignItems: "center",
    marginTop: height * 0.25,
  },
  successCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "#B794F6",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#B794F6",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  messageContainer: {
    alignItems: "center",
    marginTop: 60,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#181725",
    textAlign: "center",
    lineHeight: 36,
    marginBottom: 16,
    width: "100%",
    fontFamily: "Poppins_500Medium",
  },
  subtitle: {
    fontSize: 16,
    color: "#A0AEC0",
    textAlign: "center",
    lineHeight: 24,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 80,
    left: 20,
    right: 20,
    alignItems: "center",
  },
  trackButton: {
    backgroundColor: "#6C5CE7",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 18,
    paddingHorizontal: 40,
    borderRadius: 30,
    width: "100%",
    marginBottom: 20,
    shadowColor: "#6C5CE7",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  buttonIcon: {
    marginRight: 8,
  },
  trackButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  homeButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
  },
  homeButtonIcon: {
    marginRight: 6,
  },
  homeButtonText: {
    color: "#666",
    fontSize: 16,
    fontWeight: "500",
  },
});

export default OrderSuccessScreen;
