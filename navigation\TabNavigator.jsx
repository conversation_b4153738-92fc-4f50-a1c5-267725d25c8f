import React from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { Image, Text, View } from "react-native";
import {
  AntDesign,
  EvilIcons,
  Feather,
  FontAwesome,
  MaterialCommunityIcons,
  MaterialIcons,
  Octicons,
} from "@expo/vector-icons";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { TabIcons, tabNvigatorIcons } from "../utils/svgs";
import HomeNavigator from "./homeNavigator";
import Browse from "../screens/Browse";
import FavoriteNavigator from "./FavoriteNavigator";
import CartNavigator from "./CartNavigator";
import { AppColors } from "../utils/AppColors";

// TEST

import OrderSuccessScreen from "../components/screens/OrderSuccessScreen";
import OrderFailedScreen from "../components/screens/OrderFailedScreen";
import OrderStatusScreen from "../components/screens/OrderStatusScreen";

const Tab = createBottomTabNavigator();

const TabNavigator = () => {
  const insets = useSafeAreaInsets();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused }) => {
          let iconComponent;
          let label = "";
          if (route.name === "Discover") {
            iconComponent = focused ? (
              <TabIcons.DiscoverActive />
            ) : (
              <TabIcons.Discover />
            );
            label = "Home";
          } else if (route.name === "Browse") {
            iconComponent = focused ? (
              <TabIcons.SearchActive />
            ) : (
              <TabIcons.Search />
            );
            label = "Browse";
          } else if (route.name === "Favorites") {
            iconComponent = focused ? (
              <TabIcons.FavoriteActive />
            ) : (
              <TabIcons.Favorite />
            );
            label = "Favorites";
          } else if (route.name === "Cart") {
            iconComponent = focused ? (
              <TabIcons.CartActive />
            ) : (
              <TabIcons.Cart />
            );
            label = "Cart";
          }

          return iconComponent;
        },
        tabBarHideOnKeyboard: true,

        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: "bold",
          color: AppColors.textColor,
          marginTop: 2,
        },
        tabBarStyle: {
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: AppColors.whiteColor,
          shadowOpacity: 0.2,
          shadowRadius: 20,
          elevation: 5,
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          paddingBottom: Math.max(insets.bottom, 20), // Dynamic bottom padding with minimum of 10
          paddingTop: 14,
          height: 60 + Math.max(insets.bottom, 10), // Adjust height to accommodate bottom padding
        },
      })}
    >
      <Tab.Screen
        name="Discover"
        component={OrderSuccessScreen}
        options={{ headerShown: false }}
      />
      {/* <Tab.Screen
        name="Discover"
        component={HomeNavigator}
        options={{ headerShown: false }}
      /> */}

      <Tab.Screen
        name="Browse"
        component={Browse}
        options={{
          headerShown: false,
          tabBarStyle: { display: "none" }, // Hides the bottom tab bar
        }}
      />
      <Tab.Screen
        name="Favorites"
        component={FavoriteNavigator}
        options={{ headerShown: false }}
      />
      <Tab.Screen
        name="Cart"
        component={CartNavigator}
        options={{ headerShown: false }}
      />
    </Tab.Navigator>
  );
};

export default TabNavigator;
